/**
 * 自定义路由 - 审核相关功能
 */

export default {
  routes: [
    {
      method: 'PUT',
      path: '/stories/:id/review',
      handler: 'story.review',
      config: {
        policies: [],
        middlewares: [],
      },
    },
    {
      method: 'GET',
      path: '/stories/my',
      handler: 'story.myStories',
      config: {
        policies: [],
        middlewares: [],
      },
    },
    {
      method: 'GET',
      path: '/stories/pending',
      handler: 'story.pending',
      config: {
        policies: [],
        middlewares: [],
      },
    },
  ],
};
