# 投稿审核系统权限配置说明

## 🎯 系统概述

本系统实现了完整的"投稿+审核"流程，支持用户投稿、管理员审核、状态管理等功能。

## 📋 模型字段说明

### Story（三线故事）模型
- `title`: 故事标题（必填，支持国际化）
- `content`: 故事内容（富文本，必填，支持国际化）
- `author`: 投稿作者（关联用户，必填）
- `status`: 审核状态（pending/approved/rejected，默认 pending）
- `review_comment`: 审核意见
- `reviewed_by`: 审核人（关联管理员用户）
- `reviewed_at`: 审核时间
- `published_at`: 发布时间（审核通过时自动设置）
- `cover_image`: 封面图片
- `attachments`: 附件（支持多文件）
- `video_clip`: 视频/音频片段
- `location`: 所属厂区（关联 Factory）
- `tags`: 标签（JSON 格式）
- `submission_notes`: 投稿说明

### Oral History（口述历史）模型
- 包含 Story 的所有字段，另外增加：
- `person`: 关联人物
- `factory`: 关联厂区
- `audio`: 音频文件
- `video`: 视频文件
- `transcript`: 口述记录（富文本）
- `interview_date`: 采访日期
- `interviewer`: 采访者

## 🔐 权限配置

### 1. 用户权限（Authenticated Users）
- ✅ **创建投稿**: 可以创建 Story 和 Oral History
- ✅ **查看已发布内容**: 可以查看 status=approved 的内容
- ✅ **查看自己的投稿**: 可以查看自己创建的所有投稿（不限状态）
- ✅ **编辑自己的投稿**: 可以编辑自己创建的投稿（仅限 pending 状态）
- ❌ **审核权限**: 不能修改 status 字段
- ❌ **查看他人未发布内容**: 不能查看他人的 pending/rejected 投稿

### 2. 管理员权限（Admin Users）
- ✅ **查看所有投稿**: 可以查看所有状态的投稿
- ✅ **审核投稿**: 可以修改 status、review_comment 等字段
- ✅ **管理内容**: 可以编辑、删除任何投稿
- ✅ **查看审核列表**: 可以查看待审核列表

## 🚀 API 端点

### Story API（三线故事）
```
GET /api/stories                    # 获取已发布故事列表
GET /api/stories/:id                # 获取单个故事详情
POST /api/stories                   # 创建新投稿
PUT /api/stories/:id                # 更新投稿
DELETE /api/stories/:id             # 删除投稿

# 自定义端点
GET /api/stories/my                 # 获取我的投稿
GET /api/stories/pending            # 获取待审核列表（管理员）
PUT /api/stories/:id/review         # 审核投稿（管理员）
```

### Oral History API（口述历史）
```
GET /api/oral-histories             # 获取已发布口述历史列表
GET /api/oral-histories/:id         # 获取单个口述历史详情
POST /api/oral-histories            # 创建新投稿
PUT /api/oral-histories/:id         # 更新投稿
DELETE /api/oral-histories/:id      # 删除投稿

# 自定义端点
GET /api/oral-histories/my          # 获取我的投稿
GET /api/oral-histories/pending     # 获取待审核列表（管理员）
PUT /api/oral-histories/:id/review  # 审核投稿（管理员）
```

### Person API（三线人物）
```
GET /api/persons                    # 获取已发布人物列表
GET /api/persons/:id                # 获取单个人物详情
POST /api/persons                   # 创建新投稿
PUT /api/persons/:id                # 更新投稿
DELETE /api/persons/:id             # 删除投稿

# 自定义端点
GET /api/persons/my                 # 获取我的投稿
GET /api/persons/pending            # 获取待审核列表（管理员）
PUT /api/persons/:id/review         # 审核投稿（管理员）
```

### Factory API（三线工厂）
```
GET /api/factories                  # 获取已发布工厂列表
GET /api/factories/:id              # 获取单个工厂详情
POST /api/factories                 # 创建新投稿
PUT /api/factories/:id              # 更新投稿
DELETE /api/factories/:id           # 删除投稿

# 自定义端点
GET /api/factories/my               # 获取我的投稿
GET /api/factories/pending          # 获取待审核列表（管理员）
PUT /api/factories/:id/review       # 审核投稿（管理员）
```

### Heritage Site API（三线遗址）
```
GET /api/heritage-sites             # 获取已发布遗址列表
GET /api/heritage-sites/:id         # 获取单个遗址详情
POST /api/heritage-sites            # 创建新投稿
PUT /api/heritage-sites/:id         # 更新投稿
DELETE /api/heritage-sites/:id      # 删除投稿

# 自定义端点
GET /api/heritage-sites/my          # 获取我的投稿
GET /api/heritage-sites/pending     # 获取待审核列表（管理员）
PUT /api/heritage-sites/:id/review  # 审核投稿（管理员）
GET /api/heritage-sites/map-data    # 获取地图数据（仅坐标和基本信息）
```

## 📝 前端使用示例

### 1. 获取已发布内容
```javascript
// 获取已审核通过的故事
GET /api/stories?filters[status][$eq]=approved

// 获取特定厂区的故事
GET /api/stories?filters[status][$eq]=approved&filters[location][id][$eq]=1
```

### 2. 用户投稿
```javascript
POST /api/stories
{
  "data": {
    "title": "我的三线故事",
    "content": "故事内容...",
    "submission_notes": "投稿说明"
  }
}
```

### 3. 管理员审核
```javascript
// 审核故事
PUT /api/stories/1/review
{
  "status": "approved",
  "review_comment": "内容很好，审核通过"
}

// 审核人物
PUT /api/persons/1/review
{
  "status": "rejected",
  "review_comment": "信息不够详细，请补充更多资料"
}

// 审核工厂
PUT /api/factories/1/review
{
  "status": "approved",
  "review_comment": "工厂信息完整，审核通过"
}

// 审核遗址
PUT /api/heritage-sites/1/review
{
  "status": "approved",
  "review_comment": "坐标准确，照片清晰，审核通过"
}
```

### 4. 获取特定类型的内容
```javascript
// 获取特定工厂的所有已发布故事
GET /api/stories?filters[status][$eq]=approved&filters[location][id][$eq]=1

// 获取特定人物的所有已发布口述历史
GET /api/oral-histories?filters[status][$eq]=approved&filters[person][id][$eq]=1

// 获取地图上的所有遗址点
GET /api/heritage-sites/map-data

// 获取特定类型的遗址
GET /api/heritage-sites?filters[status][$eq]=approved&filters[site_type][$eq]=factory_building
```

### 5. 用户投稿示例
```javascript
// 投稿人物信息
POST /api/persons
{
  "data": {
    "name": "张三",
    "birth_year": 1945,
    "biography": "三线建设者...",
    "position": "工程师",
    "factory": 1,
    "submission_notes": "这是我的父亲，希望能记录他的故事"
  }
}

// 投稿工厂信息
POST /api/factories
{
  "data": {
    "name": "某某机械厂",
    "code_name": "123厂",
    "location": "四川省某市",
    "founded_year": 1965,
    "industry_type": "机械制造",
    "submission_notes": "根据老照片和回忆整理"
  }
}

// 投稿遗址信息
POST /api/heritage-sites
{
  "data": {
    "name": "某厂旧址",
    "latitude": 30.123456,
    "longitude": 104.123456,
    "site_type": "factory_building",
    "current_condition": "ruins",
    "description": "工厂主要建筑遗址",
    "visit_date": "2024-01-15",
    "submission_notes": "实地探访拍摄"
  }
}
```

## 🔄 状态流转

```
用户投稿 → pending（待审核）
    ↓
管理员审核
    ├── approved（审核通过，前台可见）
    └── rejected（审核不通过，前台不可见）
```

## ⚙️ 配置步骤

1. **启动服务器**后，进入管理面板
2. **Settings → Users & Permissions Plugin → Roles**
3. **配置 Authenticated 角色权限**:
   - Story: find, findOne, create, update（仅自己的）
   - Oral-history: find, findOne, create, update（仅自己的）
4. **配置 Public 角色权限**:
   - Story: find, findOne（仅 approved）
   - Oral-history: find, findOne（仅 approved）

## 📊 数据库表结构

系统会自动创建以下表：
- `stories`: 存储三线故事
- `oral_histories`: 存储口述历史
- `up_users`: 用户表
- `admin_users`: 管理员表
- `factories`: 厂区表
- `people`: 人物表

所有表都包含完整的审核字段和关联关系。
