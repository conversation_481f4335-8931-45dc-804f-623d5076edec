# 投稿审核系统权限配置说明

## 🎯 系统概述

本系统实现了完整的"投稿+审核"流程，支持用户投稿、管理员审核、状态管理等功能。

## 📋 模型字段说明

### Story（三线故事）模型
- `title`: 故事标题（必填，支持国际化）
- `content`: 故事内容（富文本，必填，支持国际化）
- `author`: 投稿作者（关联用户，必填）
- `status`: 审核状态（pending/approved/rejected，默认 pending）
- `review_comment`: 审核意见
- `reviewed_by`: 审核人（关联管理员用户）
- `reviewed_at`: 审核时间
- `published_at`: 发布时间（审核通过时自动设置）
- `cover_image`: 封面图片
- `attachments`: 附件（支持多文件）
- `video_clip`: 视频/音频片段
- `location`: 所属厂区（关联 Factory）
- `tags`: 标签（JSON 格式）
- `submission_notes`: 投稿说明

### Oral History（口述历史）模型
- 包含 Story 的所有字段，另外增加：
- `person`: 关联人物
- `factory`: 关联厂区
- `audio`: 音频文件
- `video`: 视频文件
- `transcript`: 口述记录（富文本）
- `interview_date`: 采访日期
- `interviewer`: 采访者

## 🔐 权限配置

### 1. 用户权限（Authenticated Users）
- ✅ **创建投稿**: 可以创建 Story 和 Oral History
- ✅ **查看已发布内容**: 可以查看 status=approved 的内容
- ✅ **查看自己的投稿**: 可以查看自己创建的所有投稿（不限状态）
- ✅ **编辑自己的投稿**: 可以编辑自己创建的投稿（仅限 pending 状态）
- ❌ **审核权限**: 不能修改 status 字段
- ❌ **查看他人未发布内容**: 不能查看他人的 pending/rejected 投稿

### 2. 管理员权限（Admin Users）
- ✅ **查看所有投稿**: 可以查看所有状态的投稿
- ✅ **审核投稿**: 可以修改 status、review_comment 等字段
- ✅ **管理内容**: 可以编辑、删除任何投稿
- ✅ **查看审核列表**: 可以查看待审核列表

## 🚀 API 端点

### Story API
```
GET /api/stories                    # 获取已发布故事列表
GET /api/stories/:id                # 获取单个故事详情
POST /api/stories                   # 创建新投稿
PUT /api/stories/:id                # 更新投稿
DELETE /api/stories/:id             # 删除投稿

# 自定义端点
GET /api/stories/my                 # 获取我的投稿
GET /api/stories/pending            # 获取待审核列表（管理员）
PUT /api/stories/:id/review         # 审核投稿（管理员）
```

### Oral History API
```
GET /api/oral-histories             # 获取已发布口述历史列表
GET /api/oral-histories/:id         # 获取单个口述历史详情
POST /api/oral-histories            # 创建新投稿
PUT /api/oral-histories/:id         # 更新投稿
DELETE /api/oral-histories/:id      # 删除投稿

# 自定义端点
GET /api/oral-histories/my          # 获取我的投稿
GET /api/oral-histories/pending     # 获取待审核列表（管理员）
PUT /api/oral-histories/:id/review  # 审核投稿（管理员）
```

## 📝 前端使用示例

### 1. 获取已发布内容
```javascript
// 获取已审核通过的故事
GET /api/stories?filters[status][$eq]=approved

// 获取特定厂区的故事
GET /api/stories?filters[status][$eq]=approved&filters[location][id][$eq]=1
```

### 2. 用户投稿
```javascript
POST /api/stories
{
  "data": {
    "title": "我的三线故事",
    "content": "故事内容...",
    "submission_notes": "投稿说明"
  }
}
```

### 3. 管理员审核
```javascript
PUT /api/stories/1/review
{
  "status": "approved",
  "review_comment": "内容很好，审核通过"
}
```

## 🔄 状态流转

```
用户投稿 → pending（待审核）
    ↓
管理员审核
    ├── approved（审核通过，前台可见）
    └── rejected（审核不通过，前台不可见）
```

## ⚙️ 配置步骤

1. **启动服务器**后，进入管理面板
2. **Settings → Users & Permissions Plugin → Roles**
3. **配置 Authenticated 角色权限**:
   - Story: find, findOne, create, update（仅自己的）
   - Oral-history: find, findOne, create, update（仅自己的）
4. **配置 Public 角色权限**:
   - Story: find, findOne（仅 approved）
   - Oral-history: find, findOne（仅 approved）

## 📊 数据库表结构

系统会自动创建以下表：
- `stories`: 存储三线故事
- `oral_histories`: 存储口述历史
- `up_users`: 用户表
- `admin_users`: 管理员表
- `factories`: 厂区表
- `people`: 人物表

所有表都包含完整的审核字段和关联关系。
