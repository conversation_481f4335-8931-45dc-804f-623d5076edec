{"collectionName": "oral_histories", "info": {"singularName": "oral-history", "pluralName": "oral-histories", "displayName": "口述历史", "description": "用户投稿的口述三线历史，支持审核流程"}, "options": {"draftAndPublish": false}, "pluginOptions": {"i18n": {"localized": true}}, "attributes": {"title": {"type": "string", "required": true, "maxLength": 200, "pluginOptions": {"i18n": {"localized": true}}}, "person": {"type": "relation", "relation": "manyToOne", "target": "api::person.person"}, "factory": {"type": "relation", "relation": "manyToOne", "target": "api::factory.factory"}, "author": {"type": "relation", "relation": "manyToOne", "target": "plugin::users-permissions.user", "required": true}, "audio": {"type": "media", "multiple": false, "allowedTypes": ["audios"]}, "video": {"type": "media", "multiple": false, "allowedTypes": ["videos"]}, "transcript": {"type": "richtext", "pluginOptions": {"i18n": {"localized": true}}}, "status": {"type": "enumeration", "enum": ["pending", "approved", "rejected"], "default": "pending", "required": true}, "review_comment": {"type": "text", "maxLength": 1000}, "reviewed_by": {"type": "relation", "relation": "manyToOne", "target": "admin::user"}, "reviewed_at": {"type": "datetime"}, "published_at": {"type": "datetime"}, "attachments": {"type": "media", "multiple": true, "allowedTypes": ["files", "images"]}, "tags": {"type": "json"}, "submission_notes": {"type": "text", "maxLength": 500}, "interview_date": {"type": "date"}, "interviewer": {"type": "string", "maxLength": 100}}}