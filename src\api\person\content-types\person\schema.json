{"collectionName": "persons", "info": {"singularName": "person", "pluralName": "persons", "displayName": "Person", "description": "三线人资料"}, "options": {"draftAndPublish": false}, "attributes": {"name": {"type": "string", "required": true}, "birth_year": {"type": "integer"}, "photo": {"type": "media", "multiple": false}, "biography": {"type": "richtext"}, "factory": {"type": "relation", "relation": "manyToOne", "target": "api::factory.factory"}, "stories": {"type": "relation", "relation": "oneToMany", "target": "api::story.story"}}}