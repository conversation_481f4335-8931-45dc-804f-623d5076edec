{"collectionName": "persons", "info": {"singularName": "person", "pluralName": "persons", "displayName": "三线人物", "description": "用户投稿的三线人物信息，支持审核流程"}, "options": {"draftAndPublish": false, "timestamps": true}, "pluginOptions": {"i18n": {"localized": true}}, "attributes": {"name": {"type": "string", "required": true, "maxLength": 100, "pluginOptions": {"i18n": {"localized": true}}}, "birth_year": {"type": "integer", "min": 1900, "max": 2024}, "death_year": {"type": "integer", "min": 1900, "max": 2024}, "photo": {"type": "media", "multiple": false, "allowedTypes": ["images"]}, "biography": {"type": "richtext", "pluginOptions": {"i18n": {"localized": true}}}, "factory": {"type": "relation", "relation": "manyToOne", "target": "api::factory.factory"}, "position": {"type": "string", "maxLength": 100, "pluginOptions": {"i18n": {"localized": true}}}, "achievements": {"type": "richtext", "pluginOptions": {"i18n": {"localized": true}}}, "author": {"type": "relation", "relation": "manyToOne", "target": "plugin::users-permissions.user", "required": true}, "status": {"type": "enumeration", "enum": ["pending", "approved", "rejected"], "default": "pending", "required": true}, "review_comment": {"type": "text", "maxLength": 1000}, "reviewed_by": {"type": "relation", "relation": "manyToOne", "target": "admin::user"}, "reviewed_at": {"type": "datetime"}, "published_at": {"type": "datetime"}, "attachments": {"type": "media", "multiple": true, "allowedTypes": ["files", "images"]}, "tags": {"type": "json"}, "submission_notes": {"type": "text", "maxLength": 500}, "hometown": {"type": "string", "maxLength": 100}, "education": {"type": "string", "maxLength": 200}, "family_info": {"type": "text", "maxLength": 1000}, "createdAt": {"type": "datetime"}, "updatedAt": {"type": "datetime"}}}