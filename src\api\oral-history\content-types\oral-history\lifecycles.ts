/**
 * 口述历史模型生命周期钩子
 */

export default {
  // 创建前的钩子
  async beforeCreate(event) {
    const { data } = event.params;
    
    // 确保新投稿默认为 pending 状态
    if (!data.status) {
      data.status = 'pending';
    }
    
    // 清除发布时间（只有审核通过才设置）
    if (data.status !== 'approved') {
      data.published_at = null;
    }
  },

  // 更新前的钩子
  async beforeUpdate(event) {
    const { data, where } = event.params;
    
    // 如果状态改为 approved，自动设置发布时间
    if (data.status === 'approved' && !data.published_at) {
      data.published_at = new Date();
    }
    
    // 如果状态不是 approved，清除发布时间
    if (data.status && data.status !== 'approved') {
      data.published_at = null;
    }
  },

  // 创建后的钩子
  async afterCreate(event) {
    const { result } = event;
    
    // 可以在这里添加通知逻辑
    // 例如：通知管理员有新的投稿待审核
    if (result.status === 'pending') {
      strapi.log.info(`新口述历史投稿待审核: ${result.title} (ID: ${result.id})`);
    }
  },

  // 更新后的钩子
  async afterUpdate(event) {
    const { result } = event;
    
    // 审核状态变更后的通知逻辑
    if (result.status === 'approved') {
      strapi.log.info(`口述历史投稿已审核通过: ${result.title} (ID: ${result.id})`);
      // 可以在这里添加通知作者的逻辑
    } else if (result.status === 'rejected') {
      strapi.log.info(`口述历史投稿已被拒绝: ${result.title} (ID: ${result.id})`);
      // 可以在这里添加通知作者的逻辑
    }
  }
};
