# 模型时间戳配置确认

## ✅ 所有模型时间戳配置状态

### 1. Story（三线故事）模型
- ✅ `timestamps: true` 在 options 中配置
- ✅ `createdAt` 字段已添加
- ✅ `updatedAt` 字段已添加
- ✅ `published_at` 字段（审核通过时设置）
- ✅ `reviewed_at` 字段（审核时设置）

### 2. Oral History（口述历史）模型
- ✅ `timestamps: true` 在 options 中配置
- ✅ `createdAt` 字段已添加
- ✅ `updatedAt` 字段已添加
- ✅ `published_at` 字段（审核通过时设置）
- ✅ `reviewed_at` 字段（审核时设置）
- ✅ `interview_date` 字段（采访日期）

### 3. Person（三线人物）模型
- ✅ `timestamps: true` 在 options 中配置
- ✅ `createdAt` 字段已添加
- ✅ `updatedAt` 字段已添加
- ✅ `published_at` 字段（审核通过时设置）
- ✅ `reviewed_at` 字段（审核时设置）

### 4. Factory（三线工厂）模型
- ✅ `timestamps: true` 在 options 中配置
- ✅ `createdAt` 字段已添加
- ✅ `updatedAt` 字段已添加
- ✅ `published_at` 字段（审核通过时设置）
- ✅ `reviewed_at` 字段（审核时设置）

### 5. Heritage Site（三线遗址）模型
- ✅ `timestamps: true` 在 options 中配置
- ✅ `createdAt` 字段已添加
- ✅ `updatedAt` 字段已添加
- ✅ `published_at` 字段（审核通过时设置）
- ✅ `reviewed_at` 字段（审核时设置）
- ✅ `visit_date` 字段（实地探访日期）

## 📅 时间字段说明

### 自动时间戳
- **`createdAt`**: 记录创建时间，由 Strapi 自动管理
- **`updatedAt`**: 记录最后更新时间，由 Strapi 自动管理

### 业务时间戳
- **`published_at`**: 内容发布时间，审核通过时自动设置
- **`reviewed_at`**: 审核时间，管理员审核时自动设置

### 特定业务时间
- **`interview_date`**: 口述历史的采访日期
- **`visit_date`**: 遗址的实地探访日期

## 🔧 配置详情

### options 配置
```json
{
  "options": {
    "draftAndPublish": false,
    "timestamps": true
  }
}
```

### 时间字段定义
```json
{
  "createdAt": {
    "type": "datetime"
  },
  "updatedAt": {
    "type": "datetime"
  },
  "published_at": {
    "type": "datetime"
  },
  "reviewed_at": {
    "type": "datetime"
  }
}
```

## 📊 API 响应示例

所有模型的 API 响应都会包含完整的时间信息：

```json
{
  "data": {
    "id": 1,
    "attributes": {
      "title": "示例标题",
      "content": "示例内容",
      "status": "approved",
      "createdAt": "2024-01-15T10:30:00.000Z",
      "updatedAt": "2024-01-16T14:20:00.000Z",
      "published_at": "2024-01-16T14:20:00.000Z",
      "reviewed_at": "2024-01-16T14:20:00.000Z"
    }
  }
}
```

## ✅ 确认结果

**所有 5 个模型都已正确配置时间戳字段：**
1. ✅ Story
2. ✅ Oral History  
3. ✅ Person
4. ✅ Factory
5. ✅ Heritage Site

每个模型都包含：
- 自动时间戳（createdAt, updatedAt）
- 业务时间戳（published_at, reviewed_at）
- 特定业务时间字段（根据模型需要）
