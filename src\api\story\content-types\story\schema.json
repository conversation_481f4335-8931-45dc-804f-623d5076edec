{"collectionName": "stories", "info": {"singularName": "story", "pluralName": "stories", "displayName": "三线故事", "description": "用户投稿的三线故事，支持审核流程"}, "options": {"draftAndPublish": false, "timestamps": true}, "pluginOptions": {"i18n": {"localized": true}}, "attributes": {"title": {"type": "string", "required": true, "maxLength": 200, "pluginOptions": {"i18n": {"localized": true}}}, "content": {"type": "richtext", "required": true, "pluginOptions": {"i18n": {"localized": true}}}, "author": {"type": "relation", "relation": "manyToOne", "target": "plugin::users-permissions.user", "required": true}, "status": {"type": "enumeration", "enum": ["pending", "approved", "rejected"], "default": "pending", "required": true}, "review_comment": {"type": "text", "maxLength": 1000}, "reviewed_by": {"type": "relation", "relation": "manyToOne", "target": "admin::user"}, "reviewed_at": {"type": "datetime"}, "published_at": {"type": "datetime"}, "cover_image": {"type": "media", "multiple": false, "allowedTypes": ["images"]}, "attachments": {"type": "media", "multiple": true, "allowedTypes": ["files", "images", "videos", "audios"]}, "video_clip": {"type": "media", "multiple": false, "allowedTypes": ["videos", "audios"]}, "location": {"type": "relation", "relation": "manyToOne", "target": "api::factory.factory"}, "tags": {"type": "json"}, "submission_notes": {"type": "text", "maxLength": 500}, "createdAt": {"type": "datetime"}, "updatedAt": {"type": "datetime"}}}