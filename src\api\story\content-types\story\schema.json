{"collectionName": "stories", "info": {"singularName": "story", "pluralName": "stories", "displayName": "Story", "description": "三线故事"}, "options": {"draftAndPublish": true}, "attributes": {"title": {"type": "string", "required": true}, "content": {"type": "richtext", "required": true}, "author": {"type": "relation", "relation": "manyToOne", "target": "plugin::users-permissions.user"}, "status": {"type": "enumeration", "enum": ["pending", "approved", "rejected"], "default": "pending"}, "review_comment": {"type": "text"}, "cover_image": {"type": "media", "multiple": false}}}