/**
 * 自定义路由 - 人物审核相关功能
 */

export default {
  routes: [
    {
      method: 'PUT',
      path: '/persons/:id/review',
      handler: 'person.review',
      config: {
        policies: [],
        middlewares: [],
      },
    },
    {
      method: 'GET',
      path: '/persons/my',
      handler: 'person.myPersons',
      config: {
        policies: [],
        middlewares: [],
      },
    },
    {
      method: 'GET',
      path: '/persons/pending',
      handler: 'person.pending',
      config: {
        policies: [],
        middlewares: [],
      },
    },
  ],
};
