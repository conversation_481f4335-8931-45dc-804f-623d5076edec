/**
 * story controller
 */

import { factories } from '@strapi/strapi';

export default factories.createCoreController('api::story.story', ({ strapi }) => ({
  // 重写 find 方法，前端默认只显示已审核通过的内容
  async find(ctx) {
    // 如果没有指定 status 过滤器，默认只显示 approved 的内容
    if (!ctx.query.filters?.status) {
      ctx.query.filters = {
        ...ctx.query.filters,
        status: { $eq: 'approved' }
      };
    }

    // 调用默认的 find 方法
    const { data, meta } = await super.find(ctx);
    return { data, meta };
  },

  // 重写 findOne 方法
  async findOne(ctx) {
    const { id } = ctx.params;
    const { user } = ctx.state;

    // 获取实体
    const entity = await strapi.entityService.findOne('api::story.story', id, {
      populate: '*'
    });

    if (!entity) {
      return ctx.notFound();
    }

    // 如果不是 approved 状态，只有作者和管理员可以查看
    if (entity.status !== 'approved') {
      if (!user || (entity.author?.id !== user.id && user.role?.type !== 'admin')) {
        return ctx.forbidden('您没有权限查看此内容');
      }
    }

    return this.sanitizeOutput(entity, ctx);
  },

  // 创建投稿
  async create(ctx) {
    const { user } = ctx.state;
    
    if (!user) {
      return ctx.unauthorized('请先登录');
    }

    // 设置作者和默认状态
    ctx.request.body.data = {
      ...ctx.request.body.data,
      author: user.id,
      status: 'pending'
    };

    const response = await super.create(ctx);
    return response;
  },

  // 审核操作
  async review(ctx) {
    const { id } = ctx.params;
    const { status, review_comment } = ctx.request.body;
    const { user } = ctx.state;

    // 检查用户权限（只有管理员可以审核）
    if (!user || user.role?.type !== 'admin') {
      return ctx.forbidden('您没有权限进行审核操作');
    }

    // 验证状态值
    if (!['approved', 'rejected'].includes(status)) {
      return ctx.badRequest('无效的审核状态');
    }

    const updateData: any = {
      status,
      review_comment,
      reviewed_by: user.id,
      reviewed_at: new Date()
    };

    // 如果审核通过，设置发布时间
    if (status === 'approved') {
      updateData.published_at = new Date();
    }

    try {
      const entity = await strapi.entityService.update('api::story.story', id, {
        data: updateData,
        populate: '*'
      });

      return this.sanitizeOutput(entity, ctx);
    } catch (error) {
      return ctx.badRequest('审核操作失败');
    }
  },

  // 获取我的投稿
  async myStories(ctx) {
    const { user } = ctx.state;
    
    if (!user) {
      return ctx.unauthorized('请先登录');
    }

    const entities = await strapi.entityService.findMany('api::story.story', {
      filters: {
        author: user.id
      },
      populate: '*',
      sort: { createdAt: 'desc' }
    });

    return this.sanitizeOutput(entities, ctx);
  },

  // 获取待审核列表（管理员专用）
  async pending(ctx) {
    const { user } = ctx.state;
    
    if (!user || user.role?.type !== 'admin') {
      return ctx.forbidden('您没有权限访问此功能');
    }

    const entities = await strapi.entityService.findMany('api::story.story', {
      filters: {
        status: 'pending'
      },
      populate: '*',
      sort: { createdAt: 'asc' }
    });

    return this.sanitizeOutput(entities, ctx);
  }
}));
