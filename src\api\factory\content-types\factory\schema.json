{"collectionName": "factories", "info": {"singularName": "factory", "pluralName": "factories", "displayName": "Factory", "description": "三线工厂资料"}, "options": {"draftAndPublish": false}, "attributes": {"name": {"type": "string", "required": true}, "code_name": {"type": "string"}, "location": {"type": "string"}, "description": {"type": "richtext"}, "founded_year": {"type": "integer"}, "latitude": {"type": "decimal"}, "longitude": {"type": "decimal"}, "persons": {"type": "relation", "relation": "oneToMany", "target": "api::person.person"}}}