{"collectionName": "factories", "info": {"singularName": "factory", "pluralName": "factories", "displayName": "三线工厂", "description": "用户投稿的三线工厂信息，支持审核流程"}, "options": {"draftAndPublish": false, "timestamps": true}, "pluginOptions": {"i18n": {"localized": true}}, "attributes": {"name": {"type": "string", "required": true, "maxLength": 200, "pluginOptions": {"i18n": {"localized": true}}}, "code_name": {"type": "string", "maxLength": 50}, "location": {"type": "string", "maxLength": 200, "pluginOptions": {"i18n": {"localized": true}}}, "description": {"type": "richtext", "pluginOptions": {"i18n": {"localized": true}}}, "founded_year": {"type": "integer", "min": 1950, "max": 2024}, "closed_year": {"type": "integer", "min": 1950, "max": 2024}, "latitude": {"type": "decimal"}, "longitude": {"type": "decimal"}, "industry_type": {"type": "string", "maxLength": 100}, "main_products": {"type": "text", "maxLength": 1000, "pluginOptions": {"i18n": {"localized": true}}}, "historical_significance": {"type": "richtext", "pluginOptions": {"i18n": {"localized": true}}}, "photos": {"type": "media", "multiple": true, "allowedTypes": ["images"]}, "author": {"type": "relation", "relation": "manyToOne", "target": "plugin::users-permissions.user", "required": true}, "status": {"type": "enumeration", "enum": ["pending", "approved", "rejected"], "default": "pending", "required": true}, "review_comment": {"type": "text", "maxLength": 1000}, "reviewed_by": {"type": "relation", "relation": "manyToOne", "target": "admin::user"}, "reviewed_at": {"type": "datetime"}, "published_at": {"type": "datetime"}, "attachments": {"type": "media", "multiple": true, "allowedTypes": ["files", "images"]}, "tags": {"type": "json"}, "submission_notes": {"type": "text", "maxLength": 500}, "current_status": {"type": "enumeration", "enum": ["active", "closed", "transformed", "unknown"]}, "createdAt": {"type": "datetime"}, "updatedAt": {"type": "datetime"}}}