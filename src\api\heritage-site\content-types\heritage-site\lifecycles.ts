/**
 * 遗址模型生命周期钩子
 */

export default {
  // 创建前的钩子
  async beforeCreate(event) {
    const { data } = event.params;
    
    // 确保新投稿默认为 pending 状态
    if (!data.status) {
      data.status = 'pending';
    }
    
    // 清除发布时间（只有审核通过才设置）
    if (data.status !== 'approved') {
      data.published_at = null;
    }

    // 验证坐标有效性
    if (data.latitude && (data.latitude < -90 || data.latitude > 90)) {
      throw new Error('纬度必须在 -90 到 90 之间');
    }
    if (data.longitude && (data.longitude < -180 || data.longitude > 180)) {
      throw new Error('经度必须在 -180 到 180 之间');
    }
  },

  // 更新前的钩子
  async beforeUpdate(event) {
    const { data, where } = event.params;
    
    // 如果状态改为 approved，自动设置发布时间
    if (data.status === 'approved' && !data.published_at) {
      data.published_at = new Date();
    }
    
    // 如果状态不是 approved，清除发布时间
    if (data.status && data.status !== 'approved') {
      data.published_at = null;
    }

    // 验证坐标有效性
    if (data.latitude && (data.latitude < -90 || data.latitude > 90)) {
      throw new Error('纬度必须在 -90 到 90 之间');
    }
    if (data.longitude && (data.longitude < -180 || data.longitude > 180)) {
      throw new Error('经度必须在 -180 到 180 之间');
    }
  },

  // 创建后的钩子
  async afterCreate(event) {
    const { result } = event;
    
    // 可以在这里添加通知逻辑
    // 例如：通知管理员有新的投稿待审核
    if (result.status === 'pending') {
      strapi.log.info(`新遗址投稿待审核: ${result.name} (ID: ${result.id})`);
    }
  },

  // 更新后的钩子
  async afterUpdate(event) {
    const { result } = event;
    
    // 审核状态变更后的通知逻辑
    if (result.status === 'approved') {
      strapi.log.info(`遗址投稿已审核通过: ${result.name} (ID: ${result.id})`);
      // 可以在这里添加通知作者的逻辑
    } else if (result.status === 'rejected') {
      strapi.log.info(`遗址投稿已被拒绝: ${result.name} (ID: ${result.id})`);
      // 可以在这里添加通知作者的逻辑
    }
  }
};
