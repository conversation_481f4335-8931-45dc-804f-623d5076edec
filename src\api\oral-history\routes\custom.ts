/**
 * 自定义路由 - 口述历史审核相关功能
 */

export default {
  routes: [
    {
      method: 'PUT',
      path: '/oral-histories/:id/review',
      handler: 'oral-history.review',
      config: {
        policies: [],
        middlewares: [],
      },
    },
    {
      method: 'GET',
      path: '/oral-histories/my',
      handler: 'oral-history.myOralHistories',
      config: {
        policies: [],
        middlewares: [],
      },
    },
    {
      method: 'GET',
      path: '/oral-histories/pending',
      handler: 'oral-history.pending',
      config: {
        policies: [],
        middlewares: [],
      },
    },
  ],
};
