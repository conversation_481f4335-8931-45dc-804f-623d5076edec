import type { StrapiApp } from '@strapi/strapi/admin';
import './extensions/custom.css';

export default {
  config: {
    head: {
      title: '天南地北三线人-傲英云',
    },
    locales: [
      'zh-Hans', // 简体中文
      'zh', // 中文
      'en', // 英语
    ],
    translations: {
      'zh-Hans': {
        'app.components.LeftMenu.navbrand.title': '天南地北三线人',
        'app.components.LeftMenu.navbrand.workplace': '傲英云管理平台',
        'global.settings': '设置',
        'Settings.global': '全局设置',
        'Settings.internationalization.title': '国际化',
        'app.components.HomePage.welcome': '欢迎来到天南地北三线人管理平台',
        'app.components.HomePage.welcome.again': '欢迎回来！',
      },
      'zh': {
        'app.components.LeftMenu.navbrand.title': '天南地北三线人',
        'app.components.LeftMenu.navbrand.workplace': '傲英云管理平台',
        'global.settings': '设置',
        'Settings.global': '全局设置',
        'Settings.internationalization.title': '国际化',
        'app.components.HomePage.welcome': '欢迎来到天南地北三线人管理平台',
        'app.components.HomePage.welcome.again': '欢迎回来！',
      }
    },
  },
  bootstrap(app: StrapiApp) {
    console.log(app);
  },
};
