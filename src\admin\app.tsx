import type { StrapiApp } from '@strapi/strapi/admin';

export default {
  config: {
    locales: [
      'zh-Hans', // 简体中文
      'zh', // 中文
      'en', // 英语
    ],
    translations: {
      'zh-Hans': {
        'app.components.LeftMenu.navbrand.title': 'Strapi 控制台',
        'app.components.LeftMenu.navbrand.workplace': '工作台',
        'global.settings': '设置',
        'Settings.global': '全局设置',
        'Settings.internationalization.title': '国际化',
      },
      'zh': {
        'app.components.LeftMenu.navbrand.title': 'Strapi 控制台',
        'app.components.LeftMenu.navbrand.workplace': '工作台',
        'global.settings': '设置',
        'Settings.global': '全局设置',
        'Settings.internationalization.title': '国际化',
      }
    },
  },
  bootstrap(app: StrapiApp) {
    console.log(app);
  },
};
