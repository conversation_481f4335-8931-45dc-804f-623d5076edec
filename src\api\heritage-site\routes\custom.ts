/**
 * 自定义路由 - 遗址审核相关功能
 */

export default {
  routes: [
    {
      method: 'PUT',
      path: '/heritage-sites/:id/review',
      handler: 'heritage-site.review',
      config: {
        policies: [],
        middlewares: [],
      },
    },
    {
      method: 'GET',
      path: '/heritage-sites/my',
      handler: 'heritage-site.myHeritageSites',
      config: {
        policies: [],
        middlewares: [],
      },
    },
    {
      method: 'GET',
      path: '/heritage-sites/pending',
      handler: 'heritage-site.pending',
      config: {
        policies: [],
        middlewares: [],
      },
    },
    {
      method: 'GET',
      path: '/heritage-sites/map-data',
      handler: 'heritage-site.mapData',
      config: {
        policies: [],
        middlewares: [],
      },
    },
  ],
};
